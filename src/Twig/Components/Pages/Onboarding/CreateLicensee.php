<?php

declare(strict_types=1);

namespace App\Twig\Components\Pages\Onboarding;

use App\Entity\Licensee;
use App\Entity\User;
use App\Security\Voter\UserEmailVerifiedVoter;
use App\Security\Voter\UserHasCompanyVoter;
use App\Security\Voter\UserHasScopeVoter;
use Symfony\Component\Security\Core\Authorization\Voter\AuthenticatedVoter;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\UX\LiveComponent\Attribute\AsLiveComponent;
use Symfony\UX\LiveComponent\Attribute\LiveProp;
use Symfony\UX\LiveComponent\ComponentWithFormTrait;
use Symfony\UX\LiveComponent\DefaultActionTrait;

#[IsGranted(AuthenticatedVoter::IS_AUTHENTICATED)]
#[IsGranted(UserHasScopeVoter::class)]
#[IsGranted(UserEmailVerifiedVoter::class)]
#[IsGranted(UserHasCompanyVoter::class)]
#[AsLiveComponent('pages:onboarding:create-licensee', template: 'components/pages/onboarding/create-licensee.html.twig')]
class CreateLicensee extends CreateProfile
{
    use DefaultActionTrait;
    use ComponentWithFormTrait;

    #[LiveProp]
    public ?Licensee $profile = null;

    protected function getProfile(): Licensee
    {
        if (null === $this->profile) {
            /** @var User $user */
            $user = $this->getUser();
            $this->profile = $this->profileService->findLicenseeByUser($user) ?: new Licensee();
        }

        return $this->profile;
    }

}
