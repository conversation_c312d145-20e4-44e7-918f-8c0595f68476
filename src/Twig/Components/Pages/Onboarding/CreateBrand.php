<?php

declare(strict_types=1);

namespace App\Twig\Components\Pages\Onboarding;

use App\Entity\Brand;
use App\Entity\User;
use App\Exception\AccessDeniedException;
use App\Security\Voter\UserEmailVerifiedVoter;
use App\Security\Voter\UserHasCompanyVoter;
use App\Security\Voter\UserHasLicensorProfileVoter;
use App\Security\Voter\UserHasScopeVoter;
use Cocur\Slugify\Slugify;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Security\Core\Authorization\Voter\AuthenticatedVoter;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\UX\LiveComponent\Attribute\AsLiveComponent;
use Symfony\UX\LiveComponent\Attribute\LiveAction;
use Symfony\UX\LiveComponent\Attribute\LiveProp;
use Symfony\UX\LiveComponent\ComponentWithFormTrait;
use Symfony\UX\LiveComponent\DefaultActionTrait;

#[IsGranted(AuthenticatedVoter::IS_AUTHENTICATED)]
#[IsGranted(UserHasScopeVoter::class)]
#[IsGranted(UserEmailVerifiedVoter::class)]
#[IsGranted(UserHasCompanyVoter::class)]
#[IsGranted(UserHasLicensorProfileVoter::class)]
#[AsLiveComponent('pages:onboarding:create-brand', template: 'components/pages/onboarding/create-brand.html.twig')]
class CreateBrand extends CreateProfile
{
    use DefaultActionTrait;
    use ComponentWithFormTrait;

    #[LiveProp]
    public ?Brand $brand = null;

    protected function getProfile(): mixed
    {
        if (null === $this->brand) {
            /** @var User $user */
            $user = $this->getUser();
            $this->brand = $this->profileService->findLicensorByUser($user)?->brands->first() ?: new Brand();
        }

        return $this->brand;
    }

    #[LiveAction]
    public function save(EntityManagerInterface $entityManager): RedirectResponse
    {
        $this->submitForm();
        $brand = $this->getForm()->getData();
        $brand->slug = new Slugify()->slugify($brand->name);

        /** @var User $user */
        $user = $this->getUser();
        $licensor = $this->profileService->findLicensorByUser($user);
        if (!$licensor) {
            throw new AccessDeniedException('app_onboarding_selectscope');
        }

        $brand->licensor = $licensor;

        $entityManager->persist($brand);
        $entityManager->flush();

        return $this->redirect($this->generateUrl('app_onboarding_createprofile_success'));
    }

}
